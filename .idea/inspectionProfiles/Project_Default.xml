<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="72">
            <item index="0" class="java.lang.String" itemvalue="pycocotools" />
            <item index="1" class="java.lang.String" itemvalue="py-cpuinfo" />
            <item index="2" class="java.lang.String" itemvalue="pandas" />
            <item index="3" class="java.lang.String" itemvalue="tqdm" />
            <item index="4" class="java.lang.String" itemvalue="scikit-image" />
            <item index="5" class="java.lang.String" itemvalue="scipy" />
            <item index="6" class="java.lang.String" itemvalue="opencv-python" />
            <item index="7" class="java.lang.String" itemvalue="av" />
            <item index="8" class="java.lang.String" itemvalue="PyYAML" />
            <item index="9" class="java.lang.String" itemvalue="decord" />
            <item index="10" class="java.lang.String" itemvalue="ipython" />
            <item index="11" class="java.lang.String" itemvalue="gradio" />
            <item index="12" class="java.lang.String" itemvalue="open-clip-torch" />
            <item index="13" class="java.lang.String" itemvalue="torchdiffeq" />
            <item index="14" class="java.lang.String" itemvalue="clean-fid" />
            <item index="15" class="java.lang.String" itemvalue="numpy" />
            <item index="16" class="java.lang.String" itemvalue="resize-right" />
            <item index="17" class="java.lang.String" itemvalue="kornia" />
            <item index="18" class="java.lang.String" itemvalue="safetensors" />
            <item index="19" class="java.lang.String" itemvalue="gfpgan" />
            <item index="20" class="java.lang.String" itemvalue="accelerate" />
            <item index="21" class="java.lang.String" itemvalue="lark" />
            <item index="22" class="java.lang.String" itemvalue="piexif" />
            <item index="23" class="java.lang.String" itemvalue="GitPython" />
            <item index="24" class="java.lang.String" itemvalue="transformers" />
            <item index="25" class="java.lang.String" itemvalue="basicsr" />
            <item index="26" class="java.lang.String" itemvalue="timm" />
            <item index="27" class="java.lang.String" itemvalue="realesrgan" />
            <item index="28" class="java.lang.String" itemvalue="diffusers" />
            <item index="29" class="java.lang.String" itemvalue="jsonmerge" />
            <item index="30" class="java.lang.String" itemvalue="tomesd" />
            <item index="31" class="java.lang.String" itemvalue="blendmodes" />
            <item index="32" class="java.lang.String" itemvalue="pytorch_lightning" />
            <item index="33" class="java.lang.String" itemvalue="omegaconf" />
            <item index="34" class="java.lang.String" itemvalue="psutil" />
            <item index="35" class="java.lang.String" itemvalue="torchsde" />
            <item index="36" class="java.lang.String" itemvalue="fastapi" />
            <item index="37" class="java.lang.String" itemvalue="einops" />
            <item index="38" class="java.lang.String" itemvalue="Pillow" />
            <item index="39" class="java.lang.String" itemvalue="httpcore" />
            <item index="40" class="java.lang.String" itemvalue="inflection" />
            <item index="41" class="java.lang.String" itemvalue="cpm_kernels" />
            <item index="42" class="java.lang.String" itemvalue="streamlit" />
            <item index="43" class="java.lang.String" itemvalue="torch" />
            <item index="44" class="java.lang.String" itemvalue="sse-starlette" />
            <item index="45" class="java.lang.String" itemvalue="SwissArmyTransformer" />
            <item index="46" class="java.lang.String" itemvalue="onnxruntime-gpu" />
            <item index="47" class="java.lang.String" itemvalue="onnxruntime" />
            <item index="48" class="java.lang.String" itemvalue="onnxsim" />
            <item index="49" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="50" class="java.lang.String" itemvalue="torchvision" />
            <item index="51" class="java.lang.String" itemvalue="onnx" />
            <item index="52" class="java.lang.String" itemvalue="gdown" />
            <item index="53" class="java.lang.String" itemvalue="htbuilder" />
            <item index="54" class="java.lang.String" itemvalue="supervision" />
            <item index="55" class="java.lang.String" itemvalue="wget" />
            <item index="56" class="java.lang.String" itemvalue="protobuf" />
            <item index="57" class="java.lang.String" itemvalue="pexpect" />
            <item index="58" class="java.lang.String" itemvalue="httpx" />
            <item index="59" class="java.lang.String" itemvalue="annotated-types" />
            <item index="60" class="java.lang.String" itemvalue="pydantic-settings" />
            <item index="61" class="java.lang.String" itemvalue="rich" />
            <item index="62" class="java.lang.String" itemvalue="typing-extensions" />
            <item index="63" class="java.lang.String" itemvalue="sniffio" />
            <item index="64" class="java.lang.String" itemvalue="pyyaml" />
            <item index="65" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="66" class="java.lang.String" itemvalue="certifi" />
            <item index="67" class="java.lang.String" itemvalue="anyio" />
            <item index="68" class="java.lang.String" itemvalue="tomli" />
            <item index="69" class="java.lang.String" itemvalue="idna" />
            <item index="70" class="java.lang.String" itemvalue="pygments" />
            <item index="71" class="java.lang.String" itemvalue="funasr" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>