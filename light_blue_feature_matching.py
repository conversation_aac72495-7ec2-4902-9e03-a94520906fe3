from lightglue import LightGlue, SuperPoint, DISK, SIFT, ALIKED, DoGHardNet
from lightglue.utils import load_image, rbd
import cv2
import numpy as np
import torch

SaveImage = False

class LightBlueInfer(object):

    def __init__(self):
        # 初始化参数
        self.get_device = torch.device(f'cuda:0' if torch.cuda.is_available() else 'cpu')
        self.resize_size = 640
        self.match_confidence = 0.25
        self.max_num_key_points = 1024 # [256, 512, 1024, 2048, 4096] 可选择 选择的点越多，需要的GPU 显存资源也越多
        self.LightGlue = None
        self.SuperPoint = None

    def init_model(self, super_point_path: str = None, light_grue_path: str = None, max_num_key_points: int = 1024,
                   device: str = 0, resize_size: int = None, match_confidence: float = 0.25):

        self.get_device = torch.device(f'cuda:{device}' if torch.cuda.is_available() else 'cpu')  # 使用 GPU 0，或者 CPU 传入的device应该为str 类型
        self.max_num_key_points = max_num_key_points
        self.match_confidence = match_confidence
        self.resize_size = resize_size

        # 加载points 模型
        self.SuperPoint = SuperPoint(max_num_keypoints=self.max_num_key_points).eval().to(self.get_device)  # load the extractor
        self.SuperPoint.load_state_dict(torch.load(super_point_path))

        # 加载match 模型
        self.LightGlue = LightGlue(features='superpoint').eval().to(self.get_device)  # features 可修改，但是superpoint 模型也得同步修改
        state_dict = torch.load(light_grue_path)
        for i in range(9):
            pattern = f"self_attn.{i}", f"transformers.{i}.self_attn"
            state_dict = {k.replace(*pattern): v for k, v in state_dict.items()}
            pattern = f"cross_attn.{i}", f"transformers.{i}.cross_attn"
            state_dict = {k.replace(*pattern): v for k, v in state_dict.items()}
        self.LightGlue.load_state_dict(state_dict, strict=False)

    def process(self, l_correct_img, r_correct_img):  # 传入校正后的左图和右图
        # numpy  类型 转为tensor 类型  np.ndarray -> torch.Tensor
        l_tensor_img = load_image(l_correct_img).to(self.get_device)
        r_tensor_img = load_image(r_correct_img).to(self.get_device)

        return l_tensor_img, r_tensor_img

    def light_blue_infer(self, l_correct_img, r_correct_img): # 传入校正后的左图和右图

        # l_src_img = l_correct_img.copy()
        # r_src_img = r_correct_img.copy()
        # if SaveImage:  # 查看保存校正后的原图
        #     pass
        # 前处理
        l_tensor_img, r_tensor_img = self.process(l_correct_img, r_correct_img)

        # 第一步： 特征提取
        # extract local features
        feats0 = self.SuperPoint.extract(l_tensor_img)  # auto-resize the image, disable with resize=None
        feats1 = self.SuperPoint.extract(r_tensor_img)

        # 第二步：matching
        # match the features
        matches01 = self.LightGlue({'image0': feats0, 'image1': feats1})
        feats0, feats1, matches01 = [rbd(x) for x in [feats0, feats1, matches01]]  # remove batch dimension
        matches = matches01['matches']  # indices with shape (K,2)

        # 第三步：置信度过滤
        # 取得匹配的置信度
        confidences = matches01['scores']  # 获取每个匹配的置信度

        # 设置置信度阈值，过滤低置信度匹配
        confidence_threshold = self.match_confidence  # 设置阈值，可根据需要进行调整
        high_confidence_matches = matches[confidences > confidence_threshold]  # 仅保留置信度高于阈值的匹配

        # 根据筛选后的匹配更新关键点
        points0 = feats0['keypoints'][high_confidence_matches[..., 0]]  # 第一张图像的匹配点
        points1 = feats1['keypoints'][high_confidence_matches[..., 1]]  # 第二张图像的匹配点

        # 将关键点转换为列表
        l_points = points0.tolist()
        r_points = points1.tolist()

        # 渲染匹配上的特征点
        if SaveImage:

            for (lx, ly), (rx, ry) in zip(l_points, r_points):
                color = np.random.randint(0, 256, size=3).tolist()
                # 在左图上标记点
                cv2.circle(l_correct_img, (int(lx), int(ly)), 5, color, -1)
                # 在右图上标记点
                cv2.circle(r_correct_img, (int(rx), int(ry)), 5, color, -1)

            cv2.imwrite('light_blue/test_img/l_match_points.jpg', l_correct_img)
            cv2.imwrite('light_blue/test_img/r_match_points.jpg', r_correct_img)

        return l_points, r_points




