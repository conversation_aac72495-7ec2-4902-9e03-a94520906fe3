from light_blue.light_blue_feature_matching import LightBlueInfer
import cv2

LightBlue_infer = LightBlueInfer()

if __name__ == '__main__':

    # 初始化模型
    get_device = str(0)
    match_confidence = 0.25
    max_num_key_points = 1024

    super_point_path = 'lightblue_model/superpoint_v1.pth'
    light_grue_path = 'lightblue_model/superpoint_lightglue.pth'

    try:
        # 加载模型
        LightBlue_infer.init_model(super_point_path=super_point_path, light_grue_path=light_grue_path,
                                   device=get_device,match_confidence=match_confidence,
                                   max_num_key_points=max_num_key_points)
        print('lightblue model load successful！！！')

    except Exception as e:
        print('lightblue model load error!!!')

    # 执行推理
    l_img_path = '校正后_L_gs0113_70cm.jpg'
    r_img_path = '校正后_R_gs0113_70cm.jpg'

    l_img = cv2.imread(l_img_path)
    r_img = cv2.imread(r_img_path)

    LightBlue_infer.light_blue_infer(l_img, r_img)
