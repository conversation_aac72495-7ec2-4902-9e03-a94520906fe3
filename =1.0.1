Looking in indexes: https://mirrors.huaweicloud.com/repository/pypi/simple
Collecting pytorch-lightning==1.3.5
  Downloading https://mirrors.huaweicloud.com/repository/pypi/packages/b6/6a/20d0bf3b967ab62333efea36fe922aaa252d1762555b4a7afb2be5bbdcbf/pytorch_lightning-1.3.5-py3-none-any.whl (808 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 808.6/808.6 kB 4.6 MB/s eta 0:00:00
Collecting ray
  Downloading https://mirrors.huaweicloud.com/repository/pypi/packages/6d/98/9289f360deb9b3d32cc9170168dff919c2f5192bf87682d5b72050206dca/ray-2.47.1-cp310-cp310-manylinux2014_x86_64.whl (68.8 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 68.8/68.8 MB 25.1 MB/s eta 0:00:00
Requirement already satisfied: numpy>=1.17.2 in /root/anaconda3/envs/yolov8/lib/python3.10/site-packages (from pytorch-lightning==1.3.5) (1.26.4)
Requirement already satisfied: torch>=1.4 in /root/anaconda3/envs/yolov8/lib/python3.10/site-packages (from pytorch-lightning==1.3.5) (2.5.1)
Collecting future>=0.17.1 (from pytorch-lightning==1.3.5)
  Downloading https://mirrors.huaweicloud.com/repository/pypi/packages/da/71/ae30dadffc90b9006d77af76b393cb9dfbfc9629f339fc1574a1c52e6806/future-1.0.0-py3-none-any.whl (491 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 491.3/491.3 kB 9.7 MB/s eta 0:00:00
Requirement already satisfied: tqdm>=4.41.0 in /root/anaconda3/envs/yolov8/lib/python3.10/site-packages (from pytorch-lightning==1.3.5) (4.67.1)
Collecting PyYAML<=5.4.1,>=5.1 (from pytorch-lightning==1.3.5)
  Downloading https://mirrors.huaweicloud.com/repository/pypi/packages/a0/a4/d63f2d7597e1a4b55aa3b4d6c5b029991d3b824b5bd331af8d4ab1ed687d/PyYAML-5.4.1.tar.gz (175 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 175.1/175.1 kB 24.9 MB/s eta 0:00:00
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'error'
